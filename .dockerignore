# Ignore .env file to prevent it from being copied to Docker container
.env

# Ignore git files
.git
.gitignore

# Ignore Python cache
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Ignore IDE files
.vscode
.idea
*.swp
*.swo
*~

# Ignore OS files
.DS_Store
Thumbs.db

# Ignore documentation
README.md
docs/
*.md

# Ignore test files
tests/
test_*.py
*_test.py

# Ignore development files
docker-compose.override.yml
.env.local
.env.development
.env.test
