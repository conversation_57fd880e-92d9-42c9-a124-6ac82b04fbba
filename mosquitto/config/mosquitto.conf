# Mosquitto MQTT Broker Configuration for Smarteye Devices

# Basic Configuration
persistence true
persistence_location /mosquitto/data/

# Logging
log_dest file /mosquitto/log/mosquitto.log
log_dest stdout
log_type error
log_type warning
log_type notice
log_type information
log_timestamp true

# Network Configuration
# MQTT Protocol (default port 1883)
listener 1883
protocol mqtt

# WebSocket Support (port 9001)
listener 9001
protocol websockets

# Security Configuration
# Allow anonymous connections for development
# In production, set this to false and configure authentication
allow_anonymous true

# Maximum connections
max_connections 1000

# Message size limits
max_packet_size 1048576  # 1MB

# Connection limits
max_inflight_messages 20
max_queued_messages 100

# Persistence settings
autosave_interval 1800  # 30 minutes
autosave_on_changes false

# QoS settings
max_qos 2
upgrade_outgoing_qos false

# Retained messages
retain_available true

# Session settings
persistent_client_expiration 1d

# Topic alias settings (not supported in this version)
# topic_alias_maximum 10

# Logging levels for debugging
# Uncomment for more verbose logging during development
# log_type debug
# log_type subscribe
# log_type unsubscribe

# Connection timeouts (not supported in this version)
# keepalive 60

# Bridge configuration (if needed for external MQTT brokers)
# connection bridge-01
# address external-broker.example.com:1883
# topic smarteye/# out 0
# topic smarteye/# in 0

# Access Control Lists (ACLs)
# Uncomment and configure for production use
# acl_file /mosquitto/config/acl.conf

# Password file (for authentication)
# Uncomment and configure for production use
# password_file /mosquitto/config/passwd

# TLS/SSL Configuration (for production)
# Uncomment and configure certificates for secure connections
# listener 8883
# protocol mqtt
# cafile /mosquitto/config/ca.crt
# certfile /mosquitto/config/server.crt
# keyfile /mosquitto/config/server.key
# require_certificate false
# use_identity_as_username false

# WebSocket TLS (for production)
# listener 9002
# protocol websockets
# cafile /mosquitto/config/ca.crt
# certfile /mosquitto/config/server.crt
# keyfile /mosquitto/config/server.key
