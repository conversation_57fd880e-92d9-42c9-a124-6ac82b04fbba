version: '3.9'

services:
    fastapi:
        build: .
        container_name: smarteye-fastapi
        restart: always
        depends_on:
            - mysql
            - redis
            - mosquitto
        environment:
            DATABASE_URL: mysql+pymysql://user:password@mysql:3306/smarteye
            REDIS_URL: redis://redis:6379/0
            MQTT_BROKER_HOST: mosquitto
            MQTT_BROKER_PORT: 1883
            REDIS_HOST: redis
            REDIS_PORT: 6379
        ports:
            - '8000:8000' # FastAPI app exposed here
        command: >
            sh -c "uvicorn main:app --host 0.0.0.0 --port 8000"

    mysql:
        image: mysql:8.0
        container_name: smarteye-mysql
        restart: always
        environment:
            MYSQL_ROOT_PASSWORD: rootpassword
            MYSQL_DATABASE: smarteye
            MYSQL_USER: user
            MYSQL_PASSWORD: password
        ports:
            - '3307:3306'
        volumes:
            - mysql_data:/var/lib/mysql

    redis:
        image: redis:7
        container_name: smarteye-redis
        restart: always
        ports:
            - '6380:6379'

    mosquitto:
        image: eclipse-mosquitto:2
        container_name: smarteye-mqtt
        restart: always
        ports:
            - '1883:1883'
            - '9001:9001' # WebSocket support (optional)
        volumes:
            - ./mosquitto/config:/mosquitto/config
            - ./mosquitto/data:/mosquitto/data
            - ./mosquitto/log:/mosquitto/log

volumes:
    mysql_data:
