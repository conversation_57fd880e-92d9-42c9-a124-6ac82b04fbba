#!/usr/bin/env python3
"""
Demo script to showcase the migration system
"""

import asyncio
import sys
import os
from pathlib import Path
from sqlalchemy import create_engine, text
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from migrate import MigrationManager

class MigrationDemo:
    def __init__(self):
        self.manager = MigrationManager()
        self.database_url = os.getenv("DATABASE_URL", "mysql+pymysql://user:password@mysql:3306/smarteye")
        self.sync_engine = create_engine(self.database_url)
    
    def print_header(self, title):
        """Print a formatted header"""
        print(f"\n{'='*60}")
        print(f"  {title}")
        print('='*60)
    
    def print_step(self, step_num, description):
        """Print a step description"""
        print(f"\n🔸 Step {step_num}: {description}")
        print('-' * 40)
    
    def check_tables(self):
        """Check what tables exist"""
        try:
            with self.sync_engine.connect() as conn:
                result = conn.execute(text("SHOW TABLES"))
                tables = [row[0] for row in result.fetchall()]
                if tables:
                    print(f"📋 Tables found: {', '.join(tables)}")
                else:
                    print("📋 No tables found")
                return tables
        except Exception as e:
            print(f"❌ Error checking tables: {e}")
            return []
    
    def check_sample_data(self):
        """Check if sample data exists"""
        try:
            with self.sync_engine.connect() as conn:
                # Check companies
                result = conn.execute(text("SELECT COUNT(*) FROM companies"))
                company_count = result.scalar()
                
                # Check devices
                result = conn.execute(text("SELECT COUNT(*) FROM devices"))
                device_count = result.scalar()
                
                print(f"📊 Sample data: {company_count} companies, {device_count} devices")
                
                if company_count > 0:
                    # Show some sample data
                    result = conn.execute(text("SELECT name, code FROM companies LIMIT 3"))
                    companies = result.fetchall()
                    print("🏢 Companies:")
                    for company in companies:
                        print(f"   - {company[0]} ({company[1]})")
                
                if device_count > 0:
                    result = conn.execute(text("SELECT device_id, name, device_type FROM devices LIMIT 5"))
                    devices = result.fetchall()
                    print("🔧 Devices:")
                    for device in devices:
                        print(f"   - {device[0]}: {device[1]} ({device[2]})")
                
        except Exception as e:
            print(f"❌ Error checking sample data: {e}")
    
    def run_demo(self):
        """Run the complete migration demo"""
        self.print_header("SMARTEYE DEVICE MQTT SERVICE - MIGRATION DEMO")
        
        print("This demo will showcase the database migration system.")
        print("It will create tables, add sample data, and demonstrate rollback.")
        
        # Step 1: Check initial state
        self.print_step(1, "Check Initial Database State")
        initial_tables = self.check_tables()
        
        # Step 2: Initialize migration system
        self.print_step(2, "Initialize Migration System")
        if self.manager.create_database_if_not_exists():
            print("✅ Database ready")
        else:
            print("❌ Database initialization failed")
            return False
        
        # Step 3: Check connection
        self.print_step(3, "Test Database Connection")
        if self.manager.check_database_connection():
            print("✅ Database connection successful")
        else:
            print("❌ Database connection failed")
            return False
        
        # Step 4: Run initial migration
        self.print_step(4, "Apply Initial Migration (Create Tables)")
        if self.manager.upgrade("0001"):
            print("✅ Initial migration applied")
            self.check_tables()
        else:
            print("❌ Initial migration failed")
            return False
        
        # Step 5: Add sample data
        self.print_step(5, "Apply Sample Data Migration")
        if self.manager.upgrade("0002"):
            print("✅ Sample data migration applied")
            self.check_sample_data()
        else:
            print("❌ Sample data migration failed")
            return False
        
        # Step 6: Show current status
        self.print_step(6, "Check Migration Status")
        print("📍 Current migration status:")
        self.manager.current()
        
        # Step 7: Demonstrate rollback
        self.print_step(7, "Demonstrate Rollback (Remove Sample Data)")
        if self.manager.downgrade("0001"):
            print("✅ Rolled back to initial migration")
            self.check_sample_data()
        else:
            print("❌ Rollback failed")
            return False
        
        # Step 8: Reapply migrations
        self.print_step(8, "Reapply All Migrations")
        if self.manager.upgrade("head"):
            print("✅ All migrations reapplied")
            self.check_sample_data()
        else:
            print("❌ Migration reapplication failed")
            return False
        
        # Step 9: Final status
        self.print_step(9, "Final Migration Status")
        print("📍 Final migration status:")
        self.manager.current()
        
        self.print_header("DEMO COMPLETED SUCCESSFULLY! 🎉")
        print("The migration system is working correctly.")
        print("\nNext steps:")
        print("1. Start the FastAPI application: uvicorn main:app --reload")
        print("2. Visit http://localhost:8000/docs to see the API")
        print("3. Use the MQTT endpoints to test device communication")
        print("4. Check the sample data in your database")
        
        return True

def main():
    """Main function"""
    demo = MigrationDemo()
    
    print("🚀 Starting Smarteye Device MQTT Service Migration Demo")
    print("\nThis demo requires:")
    print("- MySQL database running (docker-compose up mysql)")
    print("- Correct DATABASE_URL in .env file")
    print("- Python dependencies installed")
    
    input("\nPress Enter to continue or Ctrl+C to exit...")
    
    try:
        success = demo.run_demo()
        if success:
            print("\n✅ Demo completed successfully!")
            sys.exit(0)
        else:
            print("\n❌ Demo failed!")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n👋 Demo cancelled by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 Demo failed with error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
