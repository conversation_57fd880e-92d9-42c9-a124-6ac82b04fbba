#!/usr/bin/env python3
"""
Test script for database migrations
"""

import asyncio
import sys
import os
from pathlib import Path
from sqlalchemy import create_engine, text, inspect
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from migrate import MigrationManager

class MigrationTester:
    def __init__(self):
        self.manager = MigrationManager()
        self.database_url = os.getenv("DATABASE_URL", "mysql+pymysql://user:password@mysql:3306/smarteye")
        self.sync_engine = create_engine(self.database_url)
    
    def test_database_connection(self):
        """Test database connection"""
        print("🔍 Testing database connection...")
        return self.manager.check_database_connection()
    
    def test_create_database(self):
        """Test database creation"""
        print("🔍 Testing database creation...")
        return self.manager.create_database_if_not_exists()
    
    def test_migration_upgrade(self):
        """Test migration upgrade"""
        print("🔍 Testing migration upgrade...")
        return self.manager.upgrade("head")
    
    def test_tables_created(self):
        """Test if all tables were created"""
        print("🔍 Testing if tables were created...")
        try:
            inspector = inspect(self.sync_engine)
            tables = inspector.get_table_names()
            
            expected_tables = [
                'companies', 'sites', 'devices', 'tanks', 'pumps', 
                'nozzles', 'tank_logs', 'pump_logs', 'alembic_version'
            ]
            
            missing_tables = []
            for table in expected_tables:
                if table not in tables:
                    missing_tables.append(table)
            
            if missing_tables:
                print(f"❌ Missing tables: {missing_tables}")
                return False
            else:
                print(f"✅ All expected tables found: {tables}")
                return True
                
        except Exception as e:
            print(f"❌ Error checking tables: {e}")
            return False
    
    def test_table_structure(self):
        """Test table structure"""
        print("🔍 Testing table structure...")
        try:
            inspector = inspect(self.sync_engine)
            
            # Test companies table
            companies_columns = [col['name'] for col in inspector.get_columns('companies')]
            expected_companies_columns = ['id', 'name', 'code', 'created_at', 'updated_at']
            
            if not all(col in companies_columns for col in expected_companies_columns):
                print(f"❌ Companies table missing columns. Expected: {expected_companies_columns}, Found: {companies_columns}")
                return False
            
            # Test devices table
            devices_columns = [col['name'] for col in inspector.get_columns('devices')]
            expected_devices_columns = ['id', 'device_id', 'name', 'device_type', 'site_id', 'is_active', 'last_seen', 'configuration', 'created_at', 'updated_at']
            
            if not all(col in devices_columns for col in expected_devices_columns):
                print(f"❌ Devices table missing columns. Expected: {expected_devices_columns}, Found: {devices_columns}")
                return False
            
            # Test tank_logs table
            tank_logs_columns = [col['name'] for col in inspector.get_columns('tank_logs')]
            expected_tank_logs_columns = ['id', 'tank_id', 'timestamp', 'volume', 'temperature', 'water_level', 'fuel_height', 'created_at']
            
            if not all(col in tank_logs_columns for col in expected_tank_logs_columns):
                print(f"❌ Tank_logs table missing columns. Expected: {expected_tank_logs_columns}, Found: {tank_logs_columns}")
                return False
            
            print("✅ All table structures are correct")
            return True
            
        except Exception as e:
            print(f"❌ Error checking table structure: {e}")
            return False
    
    def test_foreign_keys(self):
        """Test foreign key constraints"""
        print("🔍 Testing foreign key constraints...")
        try:
            inspector = inspect(self.sync_engine)
            
            # Test devices table foreign key to sites
            devices_fks = inspector.get_foreign_keys('devices')
            sites_fk_found = any(fk['referred_table'] == 'sites' for fk in devices_fks)
            
            if not sites_fk_found:
                print("❌ Devices table missing foreign key to sites")
                return False
            
            # Test tanks table foreign key to devices
            tanks_fks = inspector.get_foreign_keys('tanks')
            devices_fk_found = any(fk['referred_table'] == 'devices' for fk in tanks_fks)
            
            if not devices_fk_found:
                print("❌ Tanks table missing foreign key to devices")
                return False
            
            print("✅ All foreign key constraints are correct")
            return True
            
        except Exception as e:
            print(f"❌ Error checking foreign keys: {e}")
            return False
    
    def test_indexes(self):
        """Test database indexes"""
        print("🔍 Testing database indexes...")
        try:
            inspector = inspect(self.sync_engine)
            
            # Test devices table indexes
            devices_indexes = inspector.get_indexes('devices')
            device_id_index_found = any('device_id' in idx['column_names'] for idx in devices_indexes)
            
            if not device_id_index_found:
                print("❌ Devices table missing device_id index")
                return False
            
            # Test tank_logs table indexes
            tank_logs_indexes = inspector.get_indexes('tank_logs')
            timestamp_index_found = any('timestamp' in idx['column_names'] for idx in tank_logs_indexes)
            
            if not timestamp_index_found:
                print("❌ Tank_logs table missing timestamp index")
                return False
            
            print("✅ All indexes are correct")
            return True
            
        except Exception as e:
            print(f"❌ Error checking indexes: {e}")
            return False
    
    def test_migration_downgrade(self):
        """Test migration downgrade"""
        print("🔍 Testing migration downgrade...")
        return self.manager.downgrade("base")
    
    def test_migration_upgrade_again(self):
        """Test migration upgrade again"""
        print("🔍 Testing migration upgrade again...")
        return self.manager.upgrade("head")
    
    def run_all_tests(self):
        """Run all migration tests"""
        print("🚀 Starting migration tests...\n")
        
        tests = [
            ("Database Connection", self.test_database_connection),
            ("Database Creation", self.test_create_database),
            ("Migration Upgrade", self.test_migration_upgrade),
            ("Tables Created", self.test_tables_created),
            ("Table Structure", self.test_table_structure),
            ("Foreign Keys", self.test_foreign_keys),
            ("Indexes", self.test_indexes),
            ("Migration Downgrade", self.test_migration_downgrade),
            ("Migration Upgrade Again", self.test_migration_upgrade_again),
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            print(f"\n{'='*50}")
            print(f"Running: {test_name}")
            print('='*50)
            
            try:
                if test_func():
                    passed += 1
                    print(f"✅ {test_name} PASSED")
                else:
                    failed += 1
                    print(f"❌ {test_name} FAILED")
            except Exception as e:
                failed += 1
                print(f"❌ {test_name} FAILED with exception: {e}")
        
        print(f"\n{'='*50}")
        print(f"TEST SUMMARY")
        print('='*50)
        print(f"✅ Passed: {passed}")
        print(f"❌ Failed: {failed}")
        print(f"📊 Total: {passed + failed}")
        
        if failed == 0:
            print("\n🎉 All migration tests passed!")
            return True
        else:
            print(f"\n💥 {failed} test(s) failed!")
            return False

def main():
    """Main function"""
    tester = MigrationTester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
