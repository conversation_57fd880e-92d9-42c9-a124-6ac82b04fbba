import urllib.parse
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import declarative_base
import os
from dotenv import load_dotenv

# Load .env file but allow environment variables to override
load_dotenv(override=False)

# Get DATABASE_URL from environment variable
# Docker environment variables take precedence over .env file
DATABASE_URL = os.getenv(
    "DATABASE_URL", "mysql+aiomysql://root:password@localhost:3306/smarteye")

# Convert pymysql to aiomysql for async support if needed
if "mysql+pymysql://" in DATABASE_URL:
    DATABASE_URL = DATABASE_URL.replace(
        "mysql+pymysql://", "mysql+aiomysql://")

print(f"Using DATABASE_URL: {DATABASE_URL}")  # For debugging

engine = create_async_engine(DATABASE_URL, echo=True, pool_pre_ping=True)
AsyncSessionLocal = async_sessionmaker(
    engine, class_=AsyncSession, expire_on_commit=False)

Base = declarative_base()


async def init_db():
    """Initialize database tables"""
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)


async def get_db():
    """Dependency to get database session"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()
