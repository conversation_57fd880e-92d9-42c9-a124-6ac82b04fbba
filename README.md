# Smarteye Device MQTT Service

IoT backend service for managing **Pumps** and **Tanks** using **MQTT**, **FastAPI**, **MySQL**, **Redis**, and **Mosquitto**.  
This service handles time-series tank logs, fuel dispense logs, and device configurations for connected IoT devices.

---

## 📂 Project Structure

smarteye-device-mqtt/
├── main.py # FastAPI application
├── routers/
│ ├── devices.py # Device management
│ ├── tanks.py # Tank management
│ ├── pumps.py # Pump management
│ ├── mqtt.py # MQTT endpoints
│ └── websocket.py # WebSocket handlers
├── models/
│ ├── pydantic/ # Pydantic models
│ └── sqlalchemy/ # SQLAlchemy models
├── database/
│ ├── connection.py # Database connection
│ └── migrations/ # Database migrations
├── mqtt/
│ ├── client.py # MQTT client
│ ├── handlers.py # Message handlers
│ └── topics.py # Topic definitions
├── services/
│ ├── device_service.py # Device management
│ └── data_service.py # Data processing
├── utils/
│ ├── redis_client.py # Redis operations
│ ├── validators.py # Data validation
│ └── helpers.py # Utility functions
├── tests/ # Unit/Integration tests
├── mosquitto/
│ └── config/
│ └── mosquitto.conf # Mosquitto broker config
├── docker-compose.yml # Local development stack
└── README.md # Project documentation

yaml
Copy code

---

## 🚀 Features

-   Manage **Companies, Sites, Devices, Pumps, Tanks, and Nozzles**
-   Devices pull configuration remotely
-   Devices push logs via **MQTT** → FastAPI consumer → **MySQL**
-   **Tank Logs** → Time series ATG (Automatic Tank Gauging)
-   **Pump Logs** → Dispense + nozzle tracking
-   **WebSocket API** for real-time dashboards
-   **Redis** for caching & fast lookups
-   **Mosquitto MQTT broker** (supports MQTT + WebSocket)

---

## 🐳 Running Locally with Docker

### 1️⃣ Clone repository

```bash
git clone https://github.com/your-org/smarteye-device-mqtt.git
cd smarteye-device-mqtt
2️⃣ Create .env file
env
Copy code
DATABASE_URL=mysql+pymysql://smarteye:smarteyepass@mysql:3306/smarteye_db
MQTT_BROKER_HOST=mosquitto
MQTT_BROKER_PORT=1883
REDIS_HOST=redis
REDIS_PORT=6379
3️⃣ Start stack
bash
Copy code
docker-compose up --build
Services started:

FastAPI → http://localhost:8000

MySQL → localhost:3306

Mosquitto MQTT → tcp://localhost:1883, ws://localhost:9001

Redis → localhost:6379

📡 MQTT Topics
smarteye/devices/{device_id}/config → Device configuration

smarteye/tanks/{tank_id}/logs → Tank logs

smarteye/pumps/{pump_id}/logs → Pump dispense logs

smarteye/nozzles/{nozzle_id}/status → Nozzle events

📖 API Documentation
Once running:

Swagger UI → http://localhost:8000/docs

ReDoc → http://localhost:8000/redoc

🧪 Running Tests
bash
Copy code
pytest tests/
🛠️ Tech Stack
FastAPI – API framework

SQLAlchemy – ORM

MySQL – Database

Mosquitto – MQTT broker

Redis – Cache / pub-sub

Docker Compose – Dev environment

🔮 Next Steps
Implement device provisioning workflow

Add monitoring/alerting for offline devices

Support multi-tenant company data isolation

Add Prometheus + Grafana for metrics

📜 License
MIT License – feel free to use and adapt.
```
