# Database Migration Guide

This guide explains how to manage database migrations for the Smarteye Device MQTT Service.

## 📋 Overview

The project uses **Alembic** for database schema migrations. Alembic is a lightweight database migration tool for use with SQLAlchemy.

## 🚀 Quick Start

### 1. Initialize Migration System

```bash
# Initialize the migration system and create database
python migrate.py init
```

This command will:
- Create the database if it doesn't exist
- Set up the migration tracking table
- Stamp the database with the initial migration

### 2. Run Migrations

```bash
# Upgrade to the latest migration
python migrate.py upgrade

# Upgrade to a specific revision
python migrate.py upgrade 0001
```

### 3. Check Current Status

```bash
# Show current migration revision
python migrate.py current

# Show migration history
python migrate.py history
```

## 🛠️ Migration Commands

### Basic Commands

| Command | Description | Example |
|---------|-------------|---------|
| `init` | Initialize migration system | `python migrate.py init` |
| `upgrade [revision]` | Upgrade to revision (default: head) | `python migrate.py upgrade` |
| `downgrade <revision>` | Downgrade to revision | `python migrate.py downgrade 0001` |
| `current` | Show current revision | `python migrate.py current` |
| `history` | Show migration history | `python migrate.py history` |
| `create <message>` | Create new migration | `python migrate.py create "Add user table"` |
| `stamp <revision>` | Stamp database with revision | `python migrate.py stamp 0001` |

### Utility Commands

| Command | Description | Example |
|---------|-------------|---------|
| `check` | Check database connection | `python migrate.py check` |
| `create-db` | Create database if not exists | `python migrate.py create-db` |
| `create-tables` | Create tables using SQLAlchemy | `python migrate.py create-tables` |

## 📁 Migration Files

### Current Migrations

1. **0001_initial_migration.py** - Creates all base tables:
   - `companies` - Company information
   - `sites` - Site/location information
   - `devices` - IoT device registry
   - `tanks` - Tank configuration
   - `pumps` - Pump configuration
   - `nozzles` - Nozzle configuration
   - `tank_logs` - Time-series tank data
   - `pump_logs` - Pump transaction logs

2. **0002_sample_data.py** - Adds sample data for testing:
   - Sample companies (Smarteye Petroleum Ltd, Demo Fuel Company)
   - Sample sites (Lagos, Abuja, Demo)
   - Sample devices (tanks and pumps)
   - Sample nozzles

### Migration File Structure

```python
"""Migration description

Revision ID: 0001
Revises: 
Create Date: 2024-01-01 00:00:00.000000
"""
from alembic import op
import sqlalchemy as sa

# revision identifiers
revision = '0001'
down_revision = None
branch_labels = None
depends_on = None

def upgrade() -> None:
    # Migration code here
    pass

def downgrade() -> None:
    # Rollback code here
    pass
```

## 🧪 Testing Migrations

### Run Migration Tests

```bash
# Run comprehensive migration tests
python test_migrations.py
```

The test script will verify:
- Database connection
- Migration upgrade/downgrade
- Table creation
- Foreign key constraints
- Index creation
- Data integrity

### Manual Testing

```bash
# 1. Start with clean database
python migrate.py downgrade base

# 2. Apply initial migration
python migrate.py upgrade 0001

# 3. Check tables were created
python migrate.py check

# 4. Apply sample data
python migrate.py upgrade 0002

# 5. Verify everything works
python test_migrations.py
```

## 🐳 Docker Environment

### Using with Docker Compose

The migration system works seamlessly with the Docker environment:

```bash
# Start the services
docker-compose up -d mysql redis mosquitto

# Wait for MySQL to be ready, then run migrations
sleep 10
python migrate.py init
python migrate.py upgrade
```

### Environment Variables

Make sure these environment variables are set in your `.env` file:

```env
DATABASE_URL=mysql+pymysql://user:password@mysql:3306/smarteye
MYSQL_URL=mysql+pymysql://user:password@mysql:3306/smarteye
```

## 🔧 Creating New Migrations

### Auto-generate Migration

```bash
# Create migration with auto-detection of model changes
python migrate.py create "Add new column to devices"
```

### Manual Migration

1. Create a new file in `database/migrations/versions/`
2. Use the next sequential number (e.g., `0003_description.py`)
3. Follow the template structure
4. Implement `upgrade()` and `downgrade()` functions

### Example: Adding a New Column

```python
def upgrade() -> None:
    op.add_column('devices', 
        sa.Column('firmware_version', sa.String(50), nullable=True)
    )

def downgrade() -> None:
    op.drop_column('devices', 'firmware_version')
```

## 🚨 Best Practices

### 1. Always Test Migrations

- Test both upgrade and downgrade
- Use the test script before deploying
- Test with sample data

### 2. Backup Before Migration

```bash
# Backup database before major migrations
mysqldump -u user -p smarteye > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 3. Migration Safety

- Never edit existing migration files
- Always create new migrations for changes
- Test migrations on staging environment first

### 4. Rollback Strategy

- Always implement `downgrade()` functions
- Test rollback procedures
- Have a rollback plan for production

## 🔍 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   ```bash
   # Check if MySQL is running
   docker-compose ps mysql
   
   # Check environment variables
   cat .env
   ```

2. **Migration Already Applied**
   ```bash
   # Check current revision
   python migrate.py current
   
   # Force stamp if needed
   python migrate.py stamp 0001
   ```

3. **Foreign Key Constraint Errors**
   ```bash
   # Check table dependencies
   # Ensure parent tables exist before child tables
   ```

### Recovery Commands

```bash
# Reset migration state
python migrate.py stamp base
python migrate.py upgrade head

# Force recreation of tables
python migrate.py create-tables
```

## 📊 Migration Status

To check the current state of your database:

```bash
# Show current revision
python migrate.py current

# Show all available migrations
python migrate.py history

# Test database connectivity
python migrate.py check
```

## 🎯 Production Deployment

### Pre-deployment Checklist

- [ ] Test migrations on staging
- [ ] Backup production database
- [ ] Verify rollback procedures
- [ ] Check for data dependencies
- [ ] Coordinate with application deployment

### Deployment Steps

1. **Backup Database**
2. **Stop Application** (if needed)
3. **Run Migrations**
4. **Verify Migration Success**
5. **Start Application**
6. **Monitor for Issues**

```bash
# Production migration command
python migrate.py upgrade head
```

This migration system provides a robust foundation for managing database schema changes in the Smarteye Device MQTT Service.
