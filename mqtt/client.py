import asyncio
import json
import logging
from typing import Dict, Any, Callable
import paho.mqtt.client as mqtt
import os
from dotenv import load_dotenv

from mqtt.topics import MQTTTopics
from mqtt.handlers import MQTTMessageHandler

load_dotenv()

logger = logging.getLogger(__name__)

class MQTTClient:
    def __init__(self):
        self.client = mqtt.Client()
        self.host = os.getenv("MQTT_BROKER_HOST", "localhost")
        self.port = int(os.getenv("MQTT_BROKER_PORT", "1883"))
        self.is_connected = False
        self.message_handler = MQTTMessageHandler()
        
        # Set up callbacks
        self.client.on_connect = self._on_connect
        self.client.on_disconnect = self._on_disconnect
        self.client.on_message = self._on_message
        
    async def connect(self):
        """Connect to MQTT broker"""
        try:
            self.client.connect(self.host, self.port, 60)
            self.client.loop_start()
            logger.info(f"Connected to MQTT broker at {self.host}:{self.port}")
        except Exception as e:
            logger.error(f"Failed to connect to MQTT broker: {e}")
            raise
    
    async def disconnect(self):
        """Disconnect from MQTT broker"""
        self.client.loop_stop()
        self.client.disconnect()
        logger.info("Disconnected from MQTT broker")
    
    def _on_connect(self, client, userdata, flags, rc):
        """Callback for when client connects to broker"""
        if rc == 0:
            self.is_connected = True
            logger.info("Successfully connected to MQTT broker")
            
            # Subscribe to all relevant topics
            self._subscribe_to_topics()
        else:
            logger.error(f"Failed to connect to MQTT broker with result code {rc}")
    
    def _on_disconnect(self, client, userdata, rc):
        """Callback for when client disconnects from broker"""
        self.is_connected = False
        logger.info("Disconnected from MQTT broker")
    
    def _on_message(self, client, userdata, msg):
        """Callback for when a message is received"""
        try:
            topic = msg.topic
            payload = json.loads(msg.payload.decode())
            
            logger.info(f"Received message on topic {topic}: {payload}")
            
            # Handle the message asynchronously
            asyncio.create_task(self.message_handler.handle_message(topic, payload))
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to decode JSON payload: {e}")
        except Exception as e:
            logger.error(f"Error processing message: {e}")
    
    def _subscribe_to_topics(self):
        """Subscribe to all relevant MQTT topics"""
        topics = [
            f"{MQTTTopics.BASE}/tanks/+/logs",
            f"{MQTTTopics.BASE}/pumps/+/logs",
            f"{MQTTTopics.BASE}/nozzles/+/status",
            f"{MQTTTopics.BASE}/nozzles/+/events",
            f"{MQTTTopics.BASE}/system/+",
        ]
        
        for topic in topics:
            self.client.subscribe(topic)
            logger.info(f"Subscribed to topic: {topic}")
    
    async def publish(self, topic: str, payload: Dict[str, Any], qos: int = 0, retain: bool = False):
        """Publish a message to MQTT broker"""
        try:
            if not self.is_connected:
                logger.warning("Not connected to MQTT broker, cannot publish message")
                return False
            
            json_payload = json.dumps(payload)
            result = self.client.publish(topic, json_payload, qos=qos, retain=retain)
            
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                logger.info(f"Published message to topic {topic}: {payload}")
                return True
            else:
                logger.error(f"Failed to publish message to topic {topic}")
                return False
                
        except Exception as e:
            logger.error(f"Error publishing message: {e}")
            return False
    
    async def publish_device_config(self, device_id: str, config: Dict[str, Any]):
        """Publish device configuration"""
        from mqtt.topics import get_device_config_topic
        topic = get_device_config_topic(device_id)
        return await self.publish(topic, config, retain=True)

# Global MQTT client instance
mqtt_client = MQTTClient()
