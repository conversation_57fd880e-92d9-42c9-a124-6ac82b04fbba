import logging
from typing import Dict, Any
from datetime import datetime

from mqtt.topics import parse_topic_tank_id, parse_topic_pump_id, parse_topic_nozzle_id
from services.data_service import DataService

logger = logging.getLogger(__name__)

class MQTTMessageHandler:
    def __init__(self):
        self.data_service = DataService()
    
    async def handle_message(self, topic: str, payload: Dict[str, Any]):
        """Route MQTT messages to appropriate handlers"""
        try:
            if "/tanks/" in topic and "/logs" in topic:
                await self._handle_tank_log(topic, payload)
            elif "/pumps/" in topic and "/logs" in topic:
                await self._handle_pump_log(topic, payload)
            elif "/nozzles/" in topic and "/status" in topic:
                await self._handle_nozzle_status(topic, payload)
            elif "/nozzles/" in topic and "/events" in topic:
                await self._handle_nozzle_event(topic, payload)
            elif "/system/" in topic:
                await self._handle_system_message(topic, payload)
            else:
                logger.warning(f"Unhandled topic: {topic}")
                
        except Exception as e:
            logger.error(f"Error handling message for topic {topic}: {e}")
    
    async def _handle_tank_log(self, topic: str, payload: Dict[str, Any]):
        """Handle tank log messages"""
        tank_id = parse_topic_tank_id(topic)
        if not tank_id:
            logger.error(f"Could not extract tank_id from topic: {topic}")
            return
        
        try:
            # Convert timestamp if it's a string
            timestamp = payload.get("timestamp")
            if isinstance(timestamp, str):
                timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            elif timestamp is None:
                timestamp = datetime.now()
            
            tank_log_data = {
                "tank_id": tank_id,
                "timestamp": timestamp,
                "volume": payload.get("volume"),
                "temperature": payload.get("temperature"),
                "water_level": payload.get("water_level"),
                "fuel_height": payload.get("fuel_height")
            }
            
            await self.data_service.create_tank_log(tank_log_data)
            logger.info(f"Processed tank log for tank {tank_id}")
            
        except Exception as e:
            logger.error(f"Error processing tank log: {e}")
    
    async def _handle_pump_log(self, topic: str, payload: Dict[str, Any]):
        """Handle pump log messages"""
        pump_id = parse_topic_pump_id(topic)
        if not pump_id:
            logger.error(f"Could not extract pump_id from topic: {topic}")
            return
        
        try:
            # Convert timestamps if they're strings
            start_time = payload.get("start_time")
            if isinstance(start_time, str):
                start_time = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
            
            end_time = payload.get("end_time")
            if isinstance(end_time, str):
                end_time = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
            
            pump_log_data = {
                "pump_id": pump_id,
                "nozzle_id": payload.get("nozzle_id"),
                "transaction_id": payload.get("transaction_id"),
                "start_time": start_time,
                "end_time": end_time,
                "volume_dispensed": payload.get("volume_dispensed"),
                "amount": payload.get("amount"),
                "price_per_liter": payload.get("price_per_liter"),
                "status": payload.get("status", "started")
            }
            
            await self.data_service.create_or_update_pump_log(pump_log_data)
            logger.info(f"Processed pump log for pump {pump_id}")
            
        except Exception as e:
            logger.error(f"Error processing pump log: {e}")
    
    async def _handle_nozzle_status(self, topic: str, payload: Dict[str, Any]):
        """Handle nozzle status messages"""
        nozzle_id = parse_topic_nozzle_id(topic)
        if not nozzle_id:
            logger.error(f"Could not extract nozzle_id from topic: {topic}")
            return
        
        try:
            # Update nozzle status in cache or database
            await self.data_service.update_nozzle_status(nozzle_id, payload)
            logger.info(f"Updated nozzle status for nozzle {nozzle_id}")
            
        except Exception as e:
            logger.error(f"Error updating nozzle status: {e}")
    
    async def _handle_nozzle_event(self, topic: str, payload: Dict[str, Any]):
        """Handle nozzle event messages"""
        nozzle_id = parse_topic_nozzle_id(topic)
        if not nozzle_id:
            logger.error(f"Could not extract nozzle_id from topic: {topic}")
            return
        
        try:
            # Process nozzle events (start dispensing, stop dispensing, etc.)
            event_type = payload.get("event_type")
            logger.info(f"Nozzle {nozzle_id} event: {event_type}")
            
            # Handle different event types
            if event_type == "dispense_start":
                await self._handle_dispense_start(nozzle_id, payload)
            elif event_type == "dispense_end":
                await self._handle_dispense_end(nozzle_id, payload)
            
        except Exception as e:
            logger.error(f"Error processing nozzle event: {e}")
    
    async def _handle_dispense_start(self, nozzle_id: str, payload: Dict[str, Any]):
        """Handle dispense start event"""
        # Create initial pump log entry
        pump_log_data = {
            "pump_id": payload.get("pump_id"),
            "nozzle_id": nozzle_id,
            "transaction_id": payload.get("transaction_id"),
            "start_time": datetime.now(),
            "status": "started"
        }
        await self.data_service.create_or_update_pump_log(pump_log_data)
    
    async def _handle_dispense_end(self, nozzle_id: str, payload: Dict[str, Any]):
        """Handle dispense end event"""
        # Update pump log entry with final values
        transaction_id = payload.get("transaction_id")
        if transaction_id:
            pump_log_data = {
                "transaction_id": transaction_id,
                "end_time": datetime.now(),
                "volume_dispensed": payload.get("volume_dispensed"),
                "amount": payload.get("amount"),
                "status": "completed"
            }
            await self.data_service.update_pump_log_by_transaction(transaction_id, pump_log_data)
    
    async def _handle_system_message(self, topic: str, payload: Dict[str, Any]):
        """Handle system-level messages"""
        logger.info(f"System message on {topic}: {payload}")
        
        # Handle system status, alerts, etc.
        if "/status" in topic:
            await self._handle_system_status(payload)
        elif "/alerts" in topic:
            await self._handle_system_alert(payload)
    
    async def _handle_system_status(self, payload: Dict[str, Any]):
        """Handle system status messages"""
        # Update system status in cache or database
        pass
    
    async def _handle_system_alert(self, payload: Dict[str, Any]):
        """Handle system alert messages"""
        # Process system alerts (low fuel, device offline, etc.)
        alert_type = payload.get("alert_type")
        logger.warning(f"System alert: {alert_type} - {payload.get('message')}")
