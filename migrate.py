#!/usr/bin/env python3
"""
Database migration management script for Smarteye Device MQTT Service
"""

import asyncio
import sys
import os
from pathlib import Path
from alembic.config import Config
from alembic import command
from alembic.script import ScriptDirectory
from alembic.runtime.environment import EnvironmentContext
from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import create_async_engine
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from database.connection import Base, engine as async_engine

class MigrationManager:
    def __init__(self):
        self.alembic_cfg = Config("alembic.ini")
        self.database_url = os.getenv("DATABASE_URL", "mysql+pymysql://user:password@mysql:3306/smarteye")
        
        # Set the database URL in alembic config
        self.alembic_cfg.set_main_option("sqlalchemy.url", self.database_url)
        
        # Create synchronous engine for Alembic
        self.sync_engine = create_engine(self.database_url)
    
    def create_migration(self, message: str, autogenerate: bool = True):
        """Create a new migration"""
        try:
            if autogenerate:
                command.revision(self.alembic_cfg, message=message, autogenerate=True)
            else:
                command.revision(self.alembic_cfg, message=message)
            print(f"✅ Created migration: {message}")
        except Exception as e:
            print(f"❌ Error creating migration: {e}")
            return False
        return True
    
    def upgrade(self, revision: str = "head"):
        """Upgrade database to a specific revision"""
        try:
            command.upgrade(self.alembic_cfg, revision)
            print(f"✅ Upgraded database to: {revision}")
        except Exception as e:
            print(f"❌ Error upgrading database: {e}")
            return False
        return True
    
    def downgrade(self, revision: str):
        """Downgrade database to a specific revision"""
        try:
            command.downgrade(self.alembic_cfg, revision)
            print(f"✅ Downgraded database to: {revision}")
        except Exception as e:
            print(f"❌ Error downgrading database: {e}")
            return False
        return True
    
    def current(self):
        """Show current revision"""
        try:
            command.current(self.alembic_cfg)
        except Exception as e:
            print(f"❌ Error getting current revision: {e}")
            return False
        return True
    
    def history(self):
        """Show migration history"""
        try:
            command.history(self.alembic_cfg)
        except Exception as e:
            print(f"❌ Error getting migration history: {e}")
            return False
        return True
    
    def stamp(self, revision: str):
        """Stamp database with a specific revision without running migrations"""
        try:
            command.stamp(self.alembic_cfg, revision)
            print(f"✅ Stamped database with revision: {revision}")
        except Exception as e:
            print(f"❌ Error stamping database: {e}")
            return False
        return True
    
    def check_database_connection(self):
        """Check if database connection is working"""
        try:
            with self.sync_engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                result.fetchone()
            print("✅ Database connection successful")
            return True
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            return False
    
    def create_database_if_not_exists(self):
        """Create database if it doesn't exist"""
        try:
            # Extract database name from URL
            db_name = self.database_url.split('/')[-1]
            base_url = self.database_url.rsplit('/', 1)[0]
            
            # Connect without specifying database
            temp_engine = create_engine(base_url + '/mysql')
            with temp_engine.connect() as conn:
                # Check if database exists
                result = conn.execute(text(f"SHOW DATABASES LIKE '{db_name}'"))
                if not result.fetchone():
                    # Create database
                    conn.execute(text(f"CREATE DATABASE {db_name}"))
                    conn.commit()
                    print(f"✅ Created database: {db_name}")
                else:
                    print(f"✅ Database {db_name} already exists")
            
            temp_engine.dispose()
            return True
        except Exception as e:
            print(f"❌ Error creating database: {e}")
            return False
    
    async def create_tables_async(self):
        """Create tables using async SQLAlchemy (alternative to migrations)"""
        try:
            async with async_engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
            print("✅ Created all tables using SQLAlchemy")
            return True
        except Exception as e:
            print(f"❌ Error creating tables: {e}")
            return False

def main():
    """Main CLI interface"""
    if len(sys.argv) < 2:
        print("Usage: python migrate.py <command> [args]")
        print("Commands:")
        print("  init                    - Initialize migration system")
        print("  create <message>        - Create new migration")
        print("  upgrade [revision]      - Upgrade to revision (default: head)")
        print("  downgrade <revision>    - Downgrade to revision")
        print("  current                 - Show current revision")
        print("  history                 - Show migration history")
        print("  stamp <revision>        - Stamp database with revision")
        print("  check                   - Check database connection")
        print("  create-db              - Create database if not exists")
        print("  create-tables          - Create tables using SQLAlchemy")
        return
    
    manager = MigrationManager()
    command_name = sys.argv[1]
    
    if command_name == "init":
        print("🚀 Initializing migration system...")
        if manager.create_database_if_not_exists():
            if manager.check_database_connection():
                # Stamp with initial migration
                manager.stamp("0001")
                print("✅ Migration system initialized")
    
    elif command_name == "create":
        if len(sys.argv) < 3:
            print("❌ Please provide a migration message")
            return
        message = sys.argv[2]
        manager.create_migration(message)
    
    elif command_name == "upgrade":
        revision = sys.argv[2] if len(sys.argv) > 2 else "head"
        manager.upgrade(revision)
    
    elif command_name == "downgrade":
        if len(sys.argv) < 3:
            print("❌ Please provide a revision to downgrade to")
            return
        revision = sys.argv[2]
        manager.downgrade(revision)
    
    elif command_name == "current":
        manager.current()
    
    elif command_name == "history":
        manager.history()
    
    elif command_name == "stamp":
        if len(sys.argv) < 3:
            print("❌ Please provide a revision to stamp")
            return
        revision = sys.argv[2]
        manager.stamp(revision)
    
    elif command_name == "check":
        manager.check_database_connection()
    
    elif command_name == "create-db":
        manager.create_database_if_not_exists()
    
    elif command_name == "create-tables":
        asyncio.run(manager.create_tables_async())
    
    else:
        print(f"❌ Unknown command: {command_name}")

if __name__ == "__main__":
    main()
