from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, desc, and_
from typing import List, Optional, Dict, Any
from datetime import datetime

from models.sqlalchemy.models import Tank<PERSON>og, PumpLog, Tank, Pump
from models.pydantic.schemas import Tank<PERSON>og<PERSON><PERSON>, PumpLogCreate, PumpLogUpdate
from utils.redis_client import redis_client

class DataService:
    def __init__(self, db: AsyncSession = None):
        self.db = db
    
    # Tank log operations
    async def create_tank_log(self, tank_log_data: Dict[str, Any]) -> TankLog:
        """Create a new tank log entry"""
        # First, get the tank by tank_id to get the database ID
        if isinstance(tank_log_data.get("tank_id"), str):
            tank_result = await self.db.execute(
                select(Tank).where(Tank.tank_id == tank_log_data["tank_id"])
            )
            tank = tank_result.scalar_one_or_none()
            if tank:
                tank_log_data["tank_id"] = tank.id
            else:
                raise ValueError(f"Tank with tank_id {tank_log_data['tank_id']} not found")
        
        db_tank_log = TankLog(**tank_log_data)
        self.db.add(db_tank_log)
        await self.db.commit()
        await self.db.refresh(db_tank_log)
        
        # Cache latest tank reading in Redis
        await self._cache_latest_tank_reading(tank_log_data["tank_id"], tank_log_data)
        
        return db_tank_log
    
    async def get_tank_logs(
        self,
        tank_id: str,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        skip: int = 0,
        limit: int = 1000
    ) -> List[TankLog]:
        """Get tank logs with optional time filtering"""
        # Get tank database ID
        tank_result = await self.db.execute(
            select(Tank).where(Tank.tank_id == tank_id)
        )
        tank = tank_result.scalar_one_or_none()
        if not tank:
            return []
        
        query = select(TankLog).where(TankLog.tank_id == tank.id)
        
        if start_time:
            query = query.where(TankLog.timestamp >= start_time)
        if end_time:
            query = query.where(TankLog.timestamp <= end_time)
        
        query = query.order_by(desc(TankLog.timestamp)).offset(skip).limit(limit)
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_latest_tank_log(self, tank_id: str) -> Optional[TankLog]:
        """Get the latest tank log entry"""
        # Try Redis cache first
        cached_data = await redis_client.get_latest_tank_reading(tank_id)
        if cached_data:
            # Convert cached data back to TankLog object if needed
            pass
        
        # Fallback to database
        tank_result = await self.db.execute(
            select(Tank).where(Tank.tank_id == tank_id)
        )
        tank = tank_result.scalar_one_or_none()
        if not tank:
            return None
        
        result = await self.db.execute(
            select(TankLog)
            .where(TankLog.tank_id == tank.id)
            .order_by(desc(TankLog.timestamp))
            .limit(1)
        )
        return result.scalar_one_or_none()
    
    # Pump log operations
    async def create_or_update_pump_log(self, pump_log_data: Dict[str, Any]) -> PumpLog:
        """Create a new pump log or update existing one by transaction_id"""
        # First, get the pump by pump_id to get the database ID
        if isinstance(pump_log_data.get("pump_id"), str):
            pump_result = await self.db.execute(
                select(Pump).where(Pump.pump_id == pump_log_data["pump_id"])
            )
            pump = pump_result.scalar_one_or_none()
            if pump:
                pump_log_data["pump_id"] = pump.id
            else:
                raise ValueError(f"Pump with pump_id {pump_log_data['pump_id']} not found")
        
        transaction_id = pump_log_data.get("transaction_id")
        if transaction_id:
            # Check if log already exists
            existing_result = await self.db.execute(
                select(PumpLog).where(PumpLog.transaction_id == transaction_id)
            )
            existing_log = existing_result.scalar_one_or_none()
            
            if existing_log:
                # Update existing log
                update_data = {k: v for k, v in pump_log_data.items() if v is not None}
                await self.db.execute(
                    update(PumpLog)
                    .where(PumpLog.transaction_id == transaction_id)
                    .values(**update_data)
                )
                await self.db.commit()
                
                # Refresh the object
                await self.db.refresh(existing_log)
                return existing_log
        
        # Create new log
        db_pump_log = PumpLog(**pump_log_data)
        self.db.add(db_pump_log)
        await self.db.commit()
        await self.db.refresh(db_pump_log)
        
        return db_pump_log
    
    async def update_pump_log_by_transaction(
        self, 
        transaction_id: str, 
        update_data: Dict[str, Any]
    ) -> Optional[PumpLog]:
        """Update pump log by transaction ID"""
        # Remove None values
        update_data = {k: v for k, v in update_data.items() if v is not None}
        
        await self.db.execute(
            update(PumpLog)
            .where(PumpLog.transaction_id == transaction_id)
            .values(**update_data)
        )
        await self.db.commit()
        
        # Return updated log
        result = await self.db.execute(
            select(PumpLog).where(PumpLog.transaction_id == transaction_id)
        )
        return result.scalar_one_or_none()
    
    async def get_pump_logs(
        self,
        pump_id: str,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        status_filter: Optional[str] = None,
        skip: int = 0,
        limit: int = 1000
    ) -> List[PumpLog]:
        """Get pump logs with optional filtering"""
        # Get pump database ID
        pump_result = await self.db.execute(
            select(Pump).where(Pump.pump_id == pump_id)
        )
        pump = pump_result.scalar_one_or_none()
        if not pump:
            return []
        
        query = select(PumpLog).where(PumpLog.pump_id == pump.id)
        
        if start_time:
            query = query.where(PumpLog.start_time >= start_time)
        if end_time:
            query = query.where(PumpLog.start_time <= end_time)
        if status_filter:
            query = query.where(PumpLog.status == status_filter)
        
        query = query.order_by(desc(PumpLog.start_time)).offset(skip).limit(limit)
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    # Nozzle status operations
    async def update_nozzle_status(self, nozzle_id: str, status_data: Dict[str, Any]):
        """Update nozzle status in cache"""
        await redis_client.set_nozzle_status(nozzle_id, status_data)
    
    async def get_nozzle_status(self, nozzle_id: str) -> Optional[Dict[str, Any]]:
        """Get nozzle status from cache"""
        return await redis_client.get_nozzle_status(nozzle_id)
    
    # Cache operations
    async def _cache_latest_tank_reading(self, tank_id: int, tank_data: Dict[str, Any]):
        """Cache the latest tank reading in Redis"""
        await redis_client.set_latest_tank_reading(tank_id, tank_data)
